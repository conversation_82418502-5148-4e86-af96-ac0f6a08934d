export type FieldType = 'text' | 'textarea' | 'radio' | 'checkbox' | 'dropdown' | 'date'

export interface FormFieldOption {
  label: string
  next_question_id?: string
  is_disqualified?: boolean
  hidden?: boolean
}

export interface Condition {
  fieldId: string
  operator: 'equals' | 'not_equals'
  value: any
}

export interface BaseField {
  id: string
  type: FieldType
  label: string
  sub_text?: string
  required?: boolean
  placeholder?: string
  conditions?: Condition[]
  maps_to_field?: string
}

export interface TextField extends BaseField {
  type: 'text'
}

export interface TextareaField extends BaseField {
  type: 'textarea'
}

export interface OptionsField extends BaseField {
  type: 'radio' | 'dropdown'
  options: FormFieldOption[]
}

export interface CheckboxField extends BaseField {
  type: 'checkbox'
  options: FormFieldOption[]
  agreement?: string
}

export interface DateField extends BaseField {
  type: 'date'
}

export type FormField = TextField | TextareaField | OptionsField | DateField | CheckboxField
