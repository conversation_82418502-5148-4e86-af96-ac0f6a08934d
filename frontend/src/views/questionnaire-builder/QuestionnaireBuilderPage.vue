<script setup lang="ts">
import { ref } from 'vue'
import FieldSidebar from './components/FieldSidebar.vue'
import FormCanvas from './components/FormCanvas.vue'
import FieldEditor from './components/FieldEditor.vue'
import FormPreview from './components/FormPreview.vue'
import { useQuestionnaireStore } from '@/stores/questionnaireStore'
import { storeToRefs } from 'pinia'

// UI Components
import { Button } from '@/components/ui/button'

const store = useQuestionnaireStore()
const { formFields } = storeToRefs(store)
const isPreview = ref(false)

function saveSchema() {
  const schema = store.getJsonSchema()
  console.log('--- FORM SCHEMA ---')
  console.log(schema)
  alert('Form schema has been logged to the console.')
}

function clearForm() {
  if (confirm('Are you sure you want to clear the form?')) {
    store.$reset()
  }
}
</script>

<template>
  <div class="p-4">
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-2xl font-bold">Questionnaire Builder</h1>
      <div class="flex space-x-2">
        <Button @click="isPreview = !isPreview" variant="outline">
          {{ isPreview ? 'Back to Editor' : 'Preview Form' }}
        </Button>
        <Button @click="saveSchema"> Save Schema </Button>
        <Button @click="clearForm" variant="destructive">Clear</Button>
      </div>
    </div>

    <div v-if="isPreview">
      <FormPreview />
    </div>
    <div v-else class="grid grid-cols-12 gap-4">
      <div class="col-span-3 sticky top-4 self-start">
        <FieldSidebar />
      </div>
      <div class="col-span-6">
        <FormCanvas />
      </div>
      <div class="col-span-3 sticky top-4 self-start">
        <FieldEditor :form-fields="formFields" />
      </div>
    </div>
  </div>
</template>
