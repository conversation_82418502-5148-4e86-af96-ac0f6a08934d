<script setup lang="ts">
import type { FormField } from '@/types/questionnaire';
import { defineProps } from 'vue';

const props = defineProps<{ field: FormField }>();
</script>

<template>
  <div>
    <label class="font-semibold">{{ props.field.label }} <span v-if="props.field.required" class="text-red-500">*</span></label>
    <p v-if="props.field.sub_text" class="text-sm text-gray-500 mt-1">{{ props.field.sub_text }}</p>
    <div class="mt-2">
      <input v-if="props.field.type === 'text'" type="text" :placeholder="props.field.placeholder" class="w-full p-2 border rounded-md" />
      <textarea v-if="props.field.type === 'textarea'" :placeholder="props.field.placeholder" class="w-full p-2 border rounded-md"></textarea>
      <div v-if="props.field.type === 'radio'" class="space-y-2">
        <div v-for="option in props.field.options" :key="option.label">
          <input type="radio" :name="props.field.id" :value="option.label" />
          <label class="ml-2">{{ option.label }}</label>
        </div>
      </div>
      <div v-if="props.field.type === 'checkbox'" class="space-y-2">
        <div v-if="(props.field as any).agreement" v-html="(props.field as any).agreement" class="prose max-w-none mb-2"></div>
        <div v-for="option in props.field.options" :key="option.label">
          <input type="checkbox" :value="option.label" />
          <label class="ml-2">{{ option.label }}</label>
        </div>
      </div>
      <select v-if="props.field.type === 'dropdown'" class="w-full p-2 border rounded-md">
        <option v-for="option in props.field.options" :key="option.label" :value="option.label">{{ option.label }}</option>
      </select>
      <input v-if="props.field.type === 'date'" type="date" class="w-full p-2 border rounded-md" />
      
    </div>
  </div>
</template>
