<script setup lang="ts">
import type { FormField } from '@/types/questionnaire'
import { defineProps } from 'vue'

// UI Components
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

const props = defineProps<{ field: FormField }>()
</script>

<template>
  <div>
    <Label class="font-semibold text-base">
      {{ props.field.label }}
      <span v-if="props.field.required" class="text-destructive">*</span>
    </Label>
    <p v-if="props.field.sub_text" class="text-sm text-muted-foreground mt-1">
      {{ props.field.sub_text }}
    </p>

    <!-- Common Question Indicator -->
    <div v-if="props.field.is_common_question" class="mt-2 space-y-1">
      <Badge variant="secondary" class="text-xs bg-green-100 text-green-800 hover:bg-green-100">
        ✓ Common Question
      </Badge>
      <Badge v-if="props.field.maps_to_field" variant="outline" class="text-xs font-mono">
        Maps to: {{ props.field.maps_to_field }}
      </Badge>
      <Badge
        v-if="props.field.negative_answer"
        variant="outline"
        class="text-xs font-mono bg-orange-50 text-orange-800 border-orange-200"
      >
        Negative: {{ props.field.negative_answer }}
      </Badge>
    </div>
    <div class="mt-2">
      <Input
        v-if="props.field.type === 'text'"
        type="text"
        :placeholder="props.field.placeholder"
        disabled
      />
      <Textarea
        v-if="props.field.type === 'textarea'"
        :placeholder="props.field.placeholder"
        disabled
      />
      <div v-if="props.field.type === 'radio'" class="space-y-2">
        <div
          v-for="option in props.field.options"
          :key="option.label"
          class="flex items-center space-x-2"
        >
          <input type="radio" :name="props.field.id" :value="option.label" disabled />
          <Label class="text-sm">{{ option.label }}</Label>
        </div>
      </div>
      <div v-if="props.field.type === 'checkbox'" class="space-y-2">
        <div
          v-if="(props.field as any).agreement"
          v-html="(props.field as any).agreement"
          class="prose max-w-none mb-2 text-sm"
        ></div>
        <div
          v-for="option in props.field.options"
          :key="option.label"
          class="flex items-center space-x-2"
        >
          <Checkbox :value="option.label" disabled />
          <Label class="text-sm">{{ option.label }}</Label>
        </div>
      </div>
      <Select v-if="props.field.type === 'dropdown'" disabled>
        <SelectTrigger>
          <SelectValue :placeholder="props.field.options?.[0]?.label || 'Select option'" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem
            v-for="option in props.field.options"
            :key="option.label"
            :value="option.label"
          >
            {{ option.label }}
          </SelectItem>
        </SelectContent>
      </Select>
      <Input v-if="props.field.type === 'date'" type="date" disabled />
    </div>
  </div>
</template>
