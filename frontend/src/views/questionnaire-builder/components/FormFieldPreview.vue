<script setup lang="ts">
import type { FormField } from '@/types/questionnaire'
import { defineProps } from 'vue'

const props = defineProps<{ field: FormField }>()
</script>

<template>
  <div>
    <label class="font-semibold"
      >{{ props.field.label }}
      <span v-if="props.field.required" class="text-red-500">*</span></label
    >
    <p v-if="props.field.sub_text" class="text-sm text-gray-500 mt-1">{{ props.field.sub_text }}</p>

    <!-- Common Question Indicator -->
    <div v-if="props.field.is_common_question" class="mt-2 space-y-1">
      <p class="text-xs text-green-600 font-semibold bg-green-50 px-2 py-1 rounded">
        ✓ Common Question
      </p>
      <p
        v-if="props.field.maps_to_field"
        class="text-xs text-blue-600 font-mono bg-blue-50 px-2 py-1 rounded"
      >
        Maps to: {{ props.field.maps_to_field }}
      </p>
      <p
        v-if="props.field.negative_answer"
        class="text-xs text-orange-600 font-mono bg-orange-50 px-2 py-1 rounded"
      >
        Negative: {{ props.field.negative_answer }}
      </p>
    </div>
    <div class="mt-2">
      <input
        v-if="props.field.type === 'text'"
        type="text"
        :placeholder="props.field.placeholder"
        class="w-full p-2 border rounded-md"
      />
      <textarea
        v-if="props.field.type === 'textarea'"
        :placeholder="props.field.placeholder"
        class="w-full p-2 border rounded-md"
      ></textarea>
      <div v-if="props.field.type === 'radio'" class="space-y-2">
        <div v-for="option in props.field.options" :key="option.label">
          <input type="radio" :name="props.field.id" :value="option.label" />
          <label class="ml-2">{{ option.label }}</label>
        </div>
      </div>
      <div v-if="props.field.type === 'checkbox'" class="space-y-2">
        <div
          v-if="(props.field as any).agreement"
          v-html="(props.field as any).agreement"
          class="prose max-w-none mb-2"
        ></div>
        <div v-for="option in props.field.options" :key="option.label">
          <input type="checkbox" :value="option.label" />
          <label class="ml-2">{{ option.label }}</label>
        </div>
      </div>
      <select v-if="props.field.type === 'dropdown'" class="w-full p-2 border rounded-md">
        <option v-for="option in props.field.options" :key="option.label" :value="option.label">
          {{ option.label }}
        </option>
      </select>
      <input v-if="props.field.type === 'date'" type="date" class="w-full p-2 border rounded-md" />
    </div>
  </div>
</template>
