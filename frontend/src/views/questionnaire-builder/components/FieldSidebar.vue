<script setup lang="ts">
import { ref } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'
import type {
  FormField,
  TextField,
  TextareaField,
  OptionsField,
  DateField,
  CheckboxField,
} from '@/types/questionnaire'

const fieldTemplates = ref<
  Array<
    | Omit<TextField, 'id'>
    | Omit<TextareaField, 'id'>
    | Omit<OptionsField, 'id'>
    | Omit<DateField, 'id'>
    | Omit<CheckboxField, 'id'>
  >
>([
  {
    type: 'text',
    label: 'Text Input',
    required: false,
    placeholder: 'Enter text...',
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'textarea',
    label: 'Textarea',
    required: false,
    placeholder: 'Enter long text...',
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'radio',
    label: 'Radio Group',
    required: false,
    options: [{ label: 'Option 1' }],
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'checkbox',
    label: 'Checkbox',
    required: false,
    options: [{ label: 'Choice 1' }],
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'dropdown',
    label: 'Dropdown',
    required: false,
    options: [{ label: 'Select 1' }],
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'date',
    label: 'Date Picker',
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
])
</script>

<template>
  <div class="p-4 border rounded-lg">
    <h2 class="text-lg font-semibold mb-4">Form Fields</h2>
    <VueDraggable
      v-model="fieldTemplates"
      :group="{ name: 'fields', pull: 'clone', put: false }"
      :sort="false"
      class="space-y-2"
    >
      <div
        v-for="(field, index) in fieldTemplates"
        :key="index"
        class="p-2 border rounded-md cursor-grab bg-gray-100 dark:bg-gray-800"
      >
        {{ field.label }}
      </div>
    </VueDraggable>
  </div>
</template>
