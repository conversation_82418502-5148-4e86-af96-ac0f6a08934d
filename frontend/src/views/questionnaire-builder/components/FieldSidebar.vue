<script setup lang="ts">
import { ref } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'
import type {
  TextField,
  TextareaField,
  OptionsField,
  DateField,
  CheckboxField,
} from '@/types/questionnaire'

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const fieldTemplates = ref<
  Array<
    | Omit<TextField, 'id'>
    | Omit<TextareaField, 'id'>
    | Omit<OptionsField, 'id'>
    | Omit<DateField, 'id'>
    | Omit<CheckboxField, 'id'>
  >
>([
  {
    type: 'text',
    label: 'Text Input',
    required: false,
    placeholder: 'Enter text...',
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'textarea',
    label: 'Textarea',
    required: false,
    placeholder: 'Enter long text...',
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'radio',
    label: 'Radio Group',
    required: false,
    options: [{ label: 'Option 1' }],
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'checkbox',
    label: 'Checkbox',
    required: false,
    options: [{ label: 'Choice 1' }],
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'dropdown',
    label: 'Dropdown',
    required: false,
    options: [{ label: 'Select 1' }],
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
  {
    type: 'date',
    label: 'Date Picker',
    is_common_question: false,
    maps_to_field: undefined,
    negative_answer: undefined,
  },
])
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>Form Fields</CardTitle>
    </CardHeader>
    <CardContent>
      <VueDraggable
        v-model="fieldTemplates"
        :group="{ name: 'fields', pull: 'clone', put: false }"
        :sort="false"
        class="space-y-2"
      >
        <Card
          v-for="(field, index) in fieldTemplates"
          :key="index"
          class="cursor-grab bg-muted/50 hover:bg-muted/70 transition-colors p-2 text-center"
        >
          {{ field.label }}
        </Card>
      </VueDraggable>
    </CardContent>
  </Card>
</template>
