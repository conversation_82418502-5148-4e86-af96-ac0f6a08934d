<script setup lang="ts">
import { useQuestionnaireStore } from '@/stores/questionnaireStore'
import { storeToRefs } from 'pinia'
import { ref, watch, defineProps } from 'vue'
import type { FormField, OptionsField } from '@/types/questionnaire'

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null
  return (...args: any[]) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

const props = defineProps<{ formFields: FormField[] }>()

const store = useQuestionnaireStore()
const { selectedField } = storeToRefs(store)
const { updateField, deleteField } = store

const editableField = ref<FormField | null>(null)

// Debounced version of the updateField store action
const debouncedUpdateField = debounce((field: FormField) => {
  updateField(field)
}, 300)

// Watch for changes in the selected field from the store to update the local copy
watch(
  selectedField,
  (newSelectedField) => {
    // Only update the editable field if the selection has actually changed to a new field
    // or has been cleared. This prevents the watcher from overwriting the user's input
    // mid-edit after a debounced save.
    if (newSelectedField?.id !== editableField.value?.id) {
      editableField.value = newSelectedField ? JSON.parse(JSON.stringify(newSelectedField)) : null
    }
  },
  { deep: true },
)

// Watch for changes in the local editable field to auto-save
watch(
  editableField,
  (newEditableField, oldEditableField) => {
    if (newEditableField && oldEditableField) {
      debouncedUpdateField(newEditableField)
    }
  },
  { deep: true },
)

const addOption = () => {
  const field = editableField.value as OptionsField
  if (field && field.options) {
    field.options.push({ label: `Option ${field.options.length + 1}` })
  }
}

function removeOption(index: number) {
  const field = editableField.value as OptionsField
  if (field && field.options) {
    field.options.splice(index, 1)
  }
}

function handleDelete() {
  if (selectedField.value) {
    deleteField(selectedField.value.id)
  }
}
</script>

<template>
  <div class="p-4 border rounded-lg">
    <h2 class="text-lg font-semibold mb-4">Field Editor</h2>
    <div v-if="editableField" class="space-y-4">
      <div>
        <label class="block font-medium">Label</label>
        <textarea
          v-model="editableField.label"
          class="w-full p-2 border rounded-md"
          rows="3"
        ></textarea>
      </div>
      <div>
        <label class="block font-medium">Sub Text</label>
        <textarea
          v-model="editableField.sub_text"
          class="w-full p-2 border rounded-md"
          rows="2"
        ></textarea>
      </div>

      <!-- Common Question Toggle -->
      <div>
        <label class="flex items-center space-x-2 cursor-pointer">
          <input
            v-model="editableField.is_common_question"
            type="checkbox"
            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <span class="font-medium">Is Common Question</span>
        </label>
      </div>

      <!-- Conditional Fields - Only show when is_common_question is enabled -->
      <div
        v-if="editableField.is_common_question"
        class="space-y-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800"
      >
        <div>
          <label class="block font-medium">Maps to Field</label>
          <input
            v-model="editableField.maps_to_field"
            type="text"
            class="w-full p-2 border rounded-md"
            placeholder="Enter field mapping (optional)"
          />
        </div>
        <div>
          <label class="block font-medium">Negative Answer</label>
          <input
            v-model="editableField.negative_answer"
            type="text"
            class="w-full p-2 border rounded-md"
            placeholder="Enter negative answer (optional)"
          />
        </div>
      </div>
      <div v-if="editableField.type === 'checkbox'">
        <label class="block font-medium">Agreement Text (HTML)</label>
        <textarea
          v-model="editableField.agreement"
          class="w-full p-2 border rounded-md"
          rows="5"
        ></textarea>
      </div>
      <div v-if="'placeholder' in editableField">
        <label class="block font-medium">Placeholder</label>
        <input
          v-model="editableField.placeholder"
          type="text"
          class="w-full p-2 border rounded-md"
        />
      </div>

      <div
        v-if="
          editableField.type === 'radio' ||
          editableField.type === 'checkbox' ||
          editableField.type === 'dropdown'
        "
      >
        <h3 class="font-medium mb-2">Options</h3>
        <div
          v-for="(option, index) in editableField.options"
          :key="index"
          class="p-3 border rounded-lg mb-3 bg-gray-50 dark:bg-gray-800"
        >
          <div class="flex items-center space-x-2">
            <input
              v-model="option.label"
              type="text"
              placeholder="Label"
              class="w-full p-2 border rounded-md"
            />
            <button @click="removeOption(index)" class="text-red-500 font-bold">X</button>
          </div>
          <div class="mt-3">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >Go to next question</label
            >
            <select v-model="option.next_question_id" class="mt-1 w-full p-2 border rounded-md">
              <option :value="undefined">End of form</option>
              <option
                v-for="field in props.formFields.filter((f) => f.id !== editableField!.id)"
                :key="field.id"
                :value="field.id"
              >
                {{ field.label }}
              </option>
            </select>
          </div>
          <div class="mt-3">
            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                v-model="option.is_disqualified"
                type="checkbox"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
              />
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >Disqualify this option</span
              >
            </label>
          </div>
        </div>
        <button @click="addOption()" class="mt-2 p-2 w-full border rounded-md">Add Option</button>
      </div>

      <button @click="handleDelete" class="mt-2 p-2 w-full bg-red-500 text-white rounded-md">
        Delete Field
      </button>
    </div>
    <div v-else class="p-8 text-center text-gray-500">Select a field to edit its properties</div>
  </div>
</template>
