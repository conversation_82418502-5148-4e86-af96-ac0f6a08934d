<script setup lang="ts">
import { useQuestionnaireStore } from '@/stores/questionnaireStore'
import { storeToRefs } from 'pinia'
import { ref, watch, defineProps } from 'vue'
import type { FormField, OptionsField } from '@/types/questionnaire'

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null
  return (...args: any[]) => {
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      fn(...args)
    }, delay)
  }
}

const props = defineProps<{ formFields: FormField[] }>()

const store = useQuestionnaireStore()
const { selectedField } = storeToRefs(store)
const { updateField, deleteField } = store

const editableField = ref<FormField | null>(null)

// Debounced version of the updateField store action
const debouncedUpdateField = debounce((field: FormField) => {
  updateField(field)
}, 300)

// Watch for changes in the selected field from the store to update the local copy
watch(
  selectedField,
  (newSelectedField) => {
    // Only update the editable field if the selection has actually changed to a new field
    // or has been cleared. This prevents the watcher from overwriting the user's input
    // mid-edit after a debounced save.
    if (newSelectedField?.id !== editableField.value?.id) {
      editableField.value = newSelectedField ? JSON.parse(JSON.stringify(newSelectedField)) : null
    }
  },
  { deep: true },
)

// Watch for changes in the local editable field to auto-save
watch(
  editableField,
  (newEditableField, oldEditableField) => {
    if (newEditableField && oldEditableField) {
      debouncedUpdateField(newEditableField)
    }
  },
  { deep: true },
)

const addOption = () => {
  const field = editableField.value as OptionsField
  if (field && field.options) {
    field.options.push({ label: `Option ${field.options.length + 1}` })
  }
}

function removeOption(index: number) {
  const field = editableField.value as OptionsField
  if (field && field.options) {
    field.options.splice(index, 1)
  }
}

function handleDelete() {
  if (selectedField.value) {
    deleteField(selectedField.value.id)
  }
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle>Field Editor</CardTitle>
    </CardHeader>
    <CardContent v-if="editableField" class="space-y-4">
      <div class="space-y-2">
        <Label>Label</Label>
        <Textarea v-model="editableField.label" :rows="3" />
      </div>
      <div class="space-y-2">
        <Label>Sub Text</Label>
        <Textarea v-model="editableField.sub_text" :rows="2" />
      </div>

      <!-- Common Question Toggle -->
      <div class="flex items-center space-x-2">
        <Checkbox
          :id="`common-question-${editableField.id}`"
          v-model:checked="editableField.is_common_question"
        />
        <Label :for="`common-question-${editableField.id}`" class="font-medium">
          Is Common Question
        </Label>
      </div>

      <!-- Conditional Fields - Only show when is_common_question is enabled -->
      <Card v-if="editableField.is_common_question" class="bg-muted/50">
        <CardContent class="space-y-4 pt-6">
          <div class="space-y-2">
            <Label>Maps to Field</Label>
            <Input
              v-model="editableField.maps_to_field"
              placeholder="Enter field mapping (optional)"
            />
          </div>
          <div class="space-y-2">
            <Label>Negative Answer</Label>
            <Input
              v-model="editableField.negative_answer"
              placeholder="Enter negative answer (optional)"
            />
          </div>
        </CardContent>
      </Card>
      <div v-if="editableField.type === 'checkbox'" class="space-y-2">
        <Label>Agreement Text (HTML)</Label>
        <Textarea v-model="editableField.agreement" :rows="5" />
      </div>
      <div v-if="'placeholder' in editableField" class="space-y-2">
        <Label>Placeholder</Label>
        <Input v-model="editableField.placeholder" />
      </div>

      <div
        v-if="
          editableField.type === 'radio' ||
          editableField.type === 'checkbox' ||
          editableField.type === 'dropdown'
        "
        class="space-y-4"
      >
        <Label class="text-base font-medium">Options</Label>
        <Card v-for="(option, index) in editableField.options" :key="index" class="bg-muted/30">
          <CardContent class="pt-4">
            <div class="flex items-center space-x-2 mb-3">
              <Input v-model="option.label" placeholder="Label" class="flex-1" />
              <Button @click="removeOption(index)" variant="destructive" size="sm"> × </Button>
            </div>
            <div class="space-y-2">
              <Label class="text-sm">Go to next question</Label>
              <Select v-model="option.next_question_id">
                <SelectTrigger>
                  <SelectValue placeholder="End of form" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem :value="undefined">End of form</SelectItem>
                  <SelectItem
                    v-for="field in props.formFields.filter((f) => f.id !== editableField!.id)"
                    :key="field.id"
                    :value="field.id"
                  >
                    {{ field.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-center space-x-2">
              <Checkbox :id="`disqualify-${index}`" v-model:checked="option.is_disqualified" />
              <Label :for="`disqualify-${index}`" class="text-sm"> Disqualify this option </Label>
            </div>
          </CardContent>
        </Card>
        <Button @click="addOption()" variant="outline" class="w-full"> Add Option </Button>
      </div>

      <Button @click="handleDelete" variant="destructive" class="w-full"> Delete Field </Button>
    </CardContent>
    <CardContent v-else class="text-center text-muted-foreground">
      Select a field to edit its properties
    </CardContent>
  </Card>
</template>
