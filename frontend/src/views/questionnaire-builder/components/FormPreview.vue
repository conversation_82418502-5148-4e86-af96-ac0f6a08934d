<script setup lang="ts">
import { useQuestionnaireStore } from '@/stores/questionnaireStore';
import { storeToRefs } from 'pinia';
import FormFieldPreview from './FormFieldPreview.vue';

const store = useQuestionnaireStore();
const { formFields } = storeToRefs(store);
</script>

<template>
  <div class="p-8 border rounded-lg bg-gray-50 dark:bg-gray-800">
    <h2 class="text-xl font-bold mb-6 text-center">Form Preview</h2>
    <div v-if="formFields.length" class="space-y-6 max-w-2xl mx-auto">
      <div v-for="field in formFields" :key="field.id">
        <FormFieldPreview :field="field" />
      </div>
      <div class="text-center pt-4">
        <button class="px-6 py-2 bg-blue-500 text-white rounded-md">Submit</button>
      </div>
    </div>
    <div v-else class="text-center text-gray-500 py-16">
      The form is empty. Add some fields in the editor.
    </div>
  </div>
</template>
