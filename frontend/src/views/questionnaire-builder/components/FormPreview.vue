<script setup lang="ts">
import { useQuestionnaireStore } from '@/stores/questionnaireStore'
import { storeToRefs } from 'pinia'
import FormFieldPreview from './FormFieldPreview.vue'

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

const store = useQuestionnaireStore()
const { formFields } = storeToRefs(store)
</script>

<template>
  <Card class="bg-muted/30">
    <CardHeader>
      <CardTitle class="text-center">Form Preview</CardTitle>
    </CardHeader>
    <CardContent>
      <div v-if="formFields.length" class="space-y-6 max-w-2xl mx-auto">
        <Card v-for="field in formFields" :key="field.id" class="bg-background">
          <CardContent>
            <FormFieldPreview :field="field" />
          </CardContent>
        </Card>
        <div class="text-center pt-4">
          <Button size="lg">Submit</Button>
        </div>
      </div>
      <div v-else class="text-center text-muted-foreground py-16">
        The form is empty. Add some fields in the editor.
      </div>
    </CardContent>
  </Card>
</template>
