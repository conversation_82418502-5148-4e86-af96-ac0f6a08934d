<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'
import { useQuestionnaireStore } from '@/stores/questionnaireStore'
import { storeToRefs } from 'pinia'
import FormFieldPreview from './FormFieldPreview.vue'

const store = useQuestionnaireStore()
const { formFields, selectedFieldId } = storeToRefs(store)
const { selectField, addField } = store

const onAdd = (event: any) => {
  const newIndex = event.newIndex
  const newFieldData = JSON.parse(JSON.stringify(formFields.value[newIndex]))

  // The library might add the cloned item directly.
  // We need to ensure it has a unique ID by replacing it with a store action.
  formFields.value.splice(newIndex, 1)
  addField(newFieldData)
}
</script>

<template>
  <div class="p-4 border rounded-lg min-h-[500px]">
    <h2 class="text-lg font-semibold mb-4">Form Canvas</h2>
    <VueDraggable
      v-model="formFields"
      class="space-y-4 p-4 border-dashed border-2 rounded-lg min-h-[400px]"
      group="fields"
      @add="onAdd"
    >
      <div
        v-for="field in formFields"
        :key="field.id"
        class="p-4 border rounded-md cursor-pointer"
        :class="{ 'ring-2 ring-blue-500': selectedFieldId === field.id }"
        @click="selectField(field.id)"
      >
        <FormFieldPreview :field="field" />
      </div>
    </VueDraggable>
    <div v-if="!formFields.length" class="text-center text-gray-500 py-16">Drop fields here</div>
  </div>
</template>
