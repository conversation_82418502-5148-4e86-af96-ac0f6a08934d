<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'
import { useQuestionnaireStore } from '@/stores/questionnaireStore'
import { storeToRefs } from 'pinia'
import FormFieldPreview from './FormFieldPreview.vue'

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const store = useQuestionnaireStore()
const { formFields, selectedFieldId } = storeToRefs(store)
const { selectField, addField } = store

const onAdd = (event: any) => {
  const newIndex = event.newIndex
  const newFieldData = JSON.parse(JSON.stringify(formFields.value[newIndex]))

  // The library might add the cloned item directly.
  // We need to ensure it has a unique ID by replacing it with a store action.
  formFields.value.splice(newIndex, 1)
  addField(newFieldData)
}
</script>

<template>
  <Card class="min-h-[500px]">
    <CardHeader>
      <CardTitle>Form Canvas</CardTitle>
    </CardHeader>
    <CardContent>
      <VueDraggable
        v-model="formFields"
        class="space-y-4 p-4 border-dashed border-2 rounded-lg min-h-[400px] border-muted-foreground/25"
        group="fields"
        @add="onAdd"
      >
        <Card
          v-for="field in formFields"
          :key="field.id"
          class="cursor-pointer transition-all hover:shadow-md"
          :class="{ 'ring-2 ring-primary': selectedFieldId === field.id }"
          @click="selectField(field.id)"
        >
          <CardContent>
            <FormFieldPreview :field="field" />
          </CardContent>
        </Card>
      </VueDraggable>
      <div v-if="!formFields.length" class="text-center text-muted-foreground py-16">
        Drop fields here
      </div>
    </CardContent>
  </Card>
</template>
