import { defineStore } from 'pinia'
import { computed, ref, type Ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type { DateRange } from 'reka-ui'
import { type SubCompany } from './subCompanyStore'

// types
export type BelugaVisit = {
  id: string
  sub_company_master_id: string
  beluga_master_id: string
  beluga_visit_type: string
  beluga_visit_mode: 'synchronous' | 'asynchronous'
  status: number
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string
  product_name: string
  strength: string
  visit_status_text: string
  visit_status_color: string
  sub_company_visit_status_text: string
  sub_company_visit_status_color: string
  sub_company: Pick<
    SubCompany,
    'first_name' | 'last_name' | 'email' | 'phone_number' | 'company_name'
  >
}

export type BelugaVisitsListPayload = {
  searchQuery?: string
  perPage?: number
  page?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
  from_date?: string
  to_date?: string
  sub_company_id?: string
}

export type BelugaVisitsListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: BelugaVisit[]
}

export type BelugaVisitDetailResponse = {
  id: string
  sub_company_id: string
  sub_company_master_id: string
  beluga_master_id: string
  beluga_visit_type: string
  beluga_visit_mode: 'synchronous' | 'asynchronous'
  beluga_visit_outcome: string | null
  status: number
  visit_status_text: string
  visit_status_color: string
  created_date_time: string
  updated_date_time: string
  appointmentScheduledLink: string
  appointmentDocName: string
  appointmentUrl: string
  appointmentDateTime: string
  synchronous_visit_status_text: string
  synchronous_visit_status_color: string
  sub_company_visit_status_text: string
  sub_company_visit_status_color: string
  medicine_data_sent_beluga: {
    name: string
    medId: string
    refills: string
    quantity: string
    strength: string
  }
  medicine_data_received_from_beluga: {
    name: string
    rxId: string
    medId: string
    refills: string
    quantity: string
    strength: string
  }
  provider_details: {
    docNPI: string
    docFirstName: string
    docLastName: string
  }
  sub_company: {
    id: string
    first_name: string
    last_name: string
    email: string
    phone_number: string
    company_name: string
  }
  beluga_visit_logs: {
    id: string
    request_from: string
    request_to: string
    event_type: string
    event_status_code: number | null
    created_date_time: string
    updated_date_time: string
  }[]
  sub_company_visit_logs: {
    id: string
    request_from: string
    request_to: string
    event_type: string
    event_status_code: number | null
    created_date_time: string
    updated_date_time: string
  }[]
}

// store definition
export const useBelugaVisitStore = defineStore('belugaVisitStore', () => {
  // state
  const isLoadingList = ref(false)
  const belugaVisits = ref<BelugaVisit[]>([])
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const filterSubCompanyId = ref<string>('all')
  const dateRange = ref({
    start: undefined,
    end: undefined,
  }) as Ref<DateRange>
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })
  const error = ref<string | null>(null)

  const subCompanyList = ref<Pick<SubCompany, 'id' | 'company_name'>[]>([])
  const isLoadingSubCompanies = ref(false)

  const belugaVisitDetail = ref<BelugaVisitDetailResponse>()
  const isLoadingVisitDetail = ref(false)

  const visitLogDetail = ref<object>({})
  const isLoadingVisitLogDetail = ref(false)

  // getters
  const listPayload = computed<Partial<BelugaVisitsListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    from_date: dateRange.value.start
      ? `${dateRange.value.start.month}/${dateRange.value.start.day}/${dateRange.value.start.year}`
      : undefined,
    to_date: dateRange.value.end
      ? `${dateRange.value.end.month}/${dateRange.value.end.day}/${dateRange.value.end.year}`
      : undefined,
    sub_company_id: filterSubCompanyId.value !== 'all' ? filterSubCompanyId.value : undefined,
  }))

  // actions
  async function fetchBelugaVisits() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse & { visitList: BelugaVisitsListResponse }>(
        '/admin/beluga-visit-list',
        listPayload.value,
      )

      if (response.data.status === 200) {
        belugaVisits.value = response.data.visitList.records

        pagination.value = {
          currentPage: response.data.visitList.current_page,
          perPage: response.data.visitList.per_page,
          totalPages: response.data.visitList.totalPage,
          totalRecords: response.data.visitList.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch beluga visits'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching beluga visits:', err)
      error.value = processErrors(err, 'An error occurred while fetching beluga visits.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getSubCompanyList() {
    isLoadingSubCompanies.value = true
    error.value = null

    try {
      const response = await apiClient.get<
        ApiResponse & { subCompanyList: Pick<SubCompany, 'id' | 'company_name'>[] }
      >('/admin/beluga-visit-sub-company-list')

      if (response.data.status === 200) {
        subCompanyList.value = response.data.subCompanyList
        return true
      } else {
        error.value = response.data.message || `Failed to fetch sub company list`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching sub company list:', err)
      error.value = processErrors(err, 'An error occurred while fetching sub company list.')
      return false
    } finally {
      isLoadingSubCompanies.value = false
    }
  }

  async function fetchBelugaVisitDetail(id: string) {
    isLoadingVisitDetail.value = true
    error.value = null

    try {
      const response = await apiClient.get<
        ApiResponse & { belugaVisitDetails: BelugaVisitDetailResponse }
      >(`/admin/beluga-visit-details/${id}`)

      if (response.data.status === 200) {
        belugaVisitDetail.value = response.data.belugaVisitDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch beluga visit detail`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching beluga visit detail:', err)
      error.value = processErrors(err, 'An error occurred while fetching beluga visit detail.')
      return false
    } finally {
      isLoadingVisitDetail.value = false
    }
  }

  async function fetchVisitLogDetail(id: string, type: 'beluga' | 'sub_company') {
    isLoadingVisitLogDetail.value = true
    error.value = null

    try {
      const url =
        type === 'beluga'
          ? `/admin/beluga-visit-event-logs/${id}`
          : `/admin/sub-company-visit-event-logs/${id}`
      const response = await apiClient.get<ApiResponse & { eventLog: unknown }>(url)

      if (response.data.status === 200) {
        visitLogDetail.value = response.data.eventLog as object
        return true
      } else {
        error.value = response.data.message || `Failed to fetch visit log detail`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching visit log detail:', err)
      error.value = processErrors(err, 'An error occurred while fetching visit log detail.')
      return false
    } finally {
      isLoadingVisitLogDetail.value = false
    }
  }

  return {
    // State
    belugaVisits,
    pagination,
    isLoadingList,
    searchQuery,
    filterSubCompanyId,
    dateRange,
    sortBy,
    error,
    subCompanyList,
    isLoadingSubCompanies,
    belugaVisitDetail,
    isLoadingVisitDetail,
    visitLogDetail,
    isLoadingVisitLogDetail,

    // Actions
    fetchBelugaVisits,
    getSubCompanyList,
    fetchBelugaVisitDetail,
    fetchVisitLogDetail,
  }
})
