import { define<PERSON>tore } from 'pinia'
import { ref, computed } from 'vue'
import type { FormField } from '@/types/questionnaire'
import { v4 as uuidv4 } from 'uuid'

export const useQuestionnaireStore = defineStore('questionnaire', () => {
  const formFields = ref<FormField[]>([])
  const selectedFieldId = ref<string | null>(null)

  const selectedField = computed(() => {
    if (!selectedFieldId.value) return null
    return formFields.value.find((f) => f.id === selectedFieldId.value) || null
  })

  function addField(field: Omit<FormField, 'id'>) {
    const newField = { ...field, id: uuidv4() } as FormField
    formFields.value.push(newField)
    selectField(newField.id)
  }

  function selectField(fieldId: string | null) {
    selectedFieldId.value = fieldId
  }

  function updateField(updatedField: FormField) {
    const index = formFields.value.findIndex((f) => f.id === updatedField.id)
    if (index !== -1) {
      formFields.value[index] = updatedField
    }
  }

  function deleteField(fieldId: string) {
    formFields.value = formFields.value.filter((f) => f.id !== fieldId)
    if (selectedFieldId.value === fieldId) {
      selectedFieldId.value = null
    }
  }

  function getJsonSchema() {
    const schema = {
      questions: formFields.value.map((field, index) => {
        const question: any = {
          id: field.id,
          slug: `question-${index + 1}`,
          question: field.label,
          answer_type: field.type,
          answer_value: '',
          meta: {
            is_sub_question: false,
          },
        }

        if (field.sub_text) {
          question.sub_text = field.sub_text
        }

        if (field.type === 'checkbox' && (field as any).agreement) {
          question.agreement = (field as any).agreement
        }

        if ('options' in field && field.options) {
          question.options = field.options.map((option, optionIndex) => {
            const optionData: any = {
              id: `${field.id}-${optionIndex + 1}`,
              value: option.label,
            }

            if (option.next_question_id) {
              optionData.next_question_id = option.next_question_id
            }

            if (option.is_disqualified) {
              optionData.is_disqualified = 1
            }

            return optionData
          })
        }

        return question
      }),
    }

    return JSON.stringify(schema, null, 2)
  }

  return {
    formFields,
    selectedFieldId,
    selectedField,
    addField,
    selectField,
    updateField,
    deleteField,
    getJsonSchema,
  }
})
